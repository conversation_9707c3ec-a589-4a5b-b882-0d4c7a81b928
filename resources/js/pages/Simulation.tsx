import { Head, router } from '@inertiajs/react';
import { useMemo, useState, useEffect } from 'react';
import { Play, FastForward, RotateCcw } from 'lucide-react';
import { simulationApi } from '@/services/simulationApi';
import { Match, SimulationProps } from '@/types/simulation';
import {
    groupFixturesByWeek,
    calculateStandings,
    calculatePredictions,
    isSeasonComplete,
    getCurrentWeek
} from '@/utils/simulationUtils';

export default function Simulation({ fixtures, seasonId }: SimulationProps) {
    // State to manage current fixtures
    const [currentFixtures, setCurrentFixtures] = useState(fixtures);
    const [isLoading, setIsLoading] = useState(false);

    // Handle both array and object with data property
    const fixturesArray = Array.isArray(currentFixtures) ? currentFixtures : currentFixtures.data || [];

    // Group fixtures by week using utility function
    const fixturesByWeek = useMemo(() => groupFixturesByWeek(fixturesArray), [fixturesArray]);
    const weekNumbers = Object.keys(fixturesByWeek).sort((a, b) => parseInt(a) - parseInt(b));

    // Calculate standings using utility function
    const standings = useMemo(() => calculateStandings(fixturesArray), [fixturesArray]);

    // Calculate championship predictions using utility function
    const predictions = useMemo(() => calculatePredictions(standings), [standings]);

    // Check if season is complete or if we're at week 6
    const seasonComplete = useMemo(() => isSeasonComplete(fixturesArray), [fixturesArray]);
    const currentWeek = useMemo(() => getCurrentWeek(fixturesArray), [fixturesArray]);
    const buttonsDisabled = seasonComplete || (currentWeek !== null && currentWeek > 6);

    const handlePlayAllWeeks = async () => {
        if (buttonsDisabled || isLoading) return;

        setIsLoading(true);
        try {
            const data = await simulationApi.playAllWeeks(seasonId);
            if (data.fixtures) {
                setCurrentFixtures(data.fixtures);
            }
        } catch (error) {
            console.error('Error playing all weeks:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handlePlayNextWeek = async () => {
        if (buttonsDisabled || isLoading) return;

        setIsLoading(true);
        try {
            const data = await simulationApi.playNextWeek(seasonId);
            if (data.fixtures) {
                setCurrentFixtures(data.fixtures);
            }
        } catch (error) {
            console.error('Error playing next week:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <>
            <Head title="League Simulation" />
            <div className="min-h-screen bg-gray-100 py-8">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="mb-6">
                        <h1 className="text-3xl font-bold text-gray-900">League Simulation</h1>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Left Column - Standings */}
                        <div className="lg:col-span-1">
                            <div className="bg-white shadow rounded-lg">
                                <div className="px-4 py-3 border-b border-gray-200">
                                    <h2 className="text-lg font-medium text-gray-900">League Table</h2>
                                </div>
                                <div className="p-4">
                                    <div className="overflow-x-auto">
                                        <table className="min-w-full text-xs">
                                            <thead>
                                                <tr className="border-b border-gray-200">
                                                    <th className="text-left py-2 font-medium text-gray-700">Pos</th>
                                                    <th className="text-left py-2 font-medium text-gray-700">Team</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">P</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">W</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">D</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">L</th>
                                                    <th className="text-center py-2 font-medium text-gray-700">Pts</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {standings.map((team, index) => (
                                                    <tr key={team.team} className="border-b border-gray-100">
                                                        <td className="py-2 text-gray-900 font-medium">{index + 1}</td>
                                                        <td className="py-2 text-gray-900">{team.team}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.played}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.won}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.drawn}</td>
                                                        <td className="py-2 text-center text-gray-600">{team.lost}</td>
                                                        <td className="py-2 text-center text-gray-900 font-bold">{team.points}</td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Middle Column - Fixtures */}
                        <div className="lg:col-span-1">
                            <div className="bg-white shadow rounded-lg">
                                <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                                    <h2 className="text-lg font-medium text-gray-900">Fixtures</h2>
                                    <div className="flex space-x-2">
                                        <button
                                            onClick={handlePlayNextWeek}
                                            disabled={buttonsDisabled || isLoading}
                                            className={`px-3 py-1 text-sm rounded ${
                                                buttonsDisabled || isLoading
                                                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                                    : 'bg-blue-600 text-white hover:bg-blue-700'
                                            }`}
                                        >
                                            {isLoading ? 'Playing...' : 'Play Next Week'}
                                        </button>
                                        <button
                                            onClick={handlePlayAllWeeks}
                                            disabled={buttonsDisabled || isLoading}
                                            className={`px-3 py-1 text-sm rounded ${
                                                buttonsDisabled || isLoading
                                                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                                    : 'bg-green-600 text-white hover:bg-green-700'
                                            }`}
                                        >
                                            {isLoading ? 'Playing...' : 'Play All Weeks'}
                                        </button>
                                    </div>
                                </div>
                                <div className="p-4 max-h-96 overflow-y-auto">
                                    {weekNumbers.map((weekNumber) => (
                                        <div key={weekNumber} className="mb-4">
                                            <h3 className="text-sm font-medium text-gray-900 mb-2">
                                                Week {weekNumber}
                                            </h3>
                                            <div className="space-y-2">
                                                {fixturesByWeek[weekNumber].map((match, index) => (
                                                    <div
                                                        key={index}
                                                        className="bg-gray-50 rounded p-2 text-sm"
                                                    >
                                                        <div className="flex items-center justify-between">
                                                            <span className="text-gray-900">{match.home_team.name}</span>
                                                            <div className="px-2">
                                                                {match.is_played ? (
                                                                    <span className="font-bold text-gray-900">
                                                                        {match.home_score} - {match.away_score}
                                                                    </span>
                                                                ) : (
                                                                    <span className="text-gray-400">vs</span>
                                                                )}
                                                            </div>
                                                            <span className="text-gray-900">{match.away_team.name}</span>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Championship Predictions */}
                        <div className="lg:col-span-1">
                            <div className="bg-white shadow rounded-lg">
                                <div className="px-4 py-3 border-b border-gray-200">
                                    <h2 className="text-lg font-medium text-gray-900">Championship Predictions</h2>
                                </div>
                                <div className="p-4">
                                    <div className="space-y-3">
                                        {Object.entries(predictions)
                                            .sort(([,a], [,b]) => b - a)
                                            .map(([team, percentage]) => (
                                            <div key={team} className="flex items-center justify-between">
                                                <span className="text-sm text-gray-900">{team}</span>
                                                <div className="flex items-center">
                                                    <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                                        <div
                                                            className="bg-blue-600 h-2 rounded-full"
                                                            style={{ width: `${percentage}%` }}
                                                        ></div>
                                                    </div>
                                                    <span className="text-sm font-medium text-gray-900 w-8">
                                                        {percentage}%
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </>
    );
}
